<!DOCTYPE html>
<html lang="ru">
<?php
use Illuminate\Support\Facades\Auth;
use App\Models\GuildInvitation;
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Битва - Echoes of Eternity</title>

    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js', 'resources/js/battle/tooltips.js', 'resources/css/battle/tooltips.css', 'resources/css/components/guild-invitation.css', 'resources/css/components/donation-button.css', 'resources/css/battle/main.css']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => $hasUnreadMessages ?? false,'unreadMessagesCount' => $unreadMessagesCount ?? 0,'hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadMessages ?? false),'unreadMessagesCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadMessagesCount ?? 0),'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>


        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <?php if(Auth::check()): ?>
            <?php
                $guildInvitation = Auth::user()->getLatestGuildInvitation();
            ?>
            <?php if (isset($component)) { $__componentOriginalf073577b00d5eaef97c81e52a3000637 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf073577b00d5eaef97c81e52a3000637 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.guild-invitation','data' => ['guildInvitation' => $guildInvitation]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.guild-invitation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['guildInvitation' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($guildInvitation)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf073577b00d5eaef97c81e52a3000637)): ?>
<?php $attributes = $__attributesOriginalf073577b00d5eaef97c81e52a3000637; ?>
<?php unset($__attributesOriginalf073577b00d5eaef97c81e52a3000637); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf073577b00d5eaef97c81e52a3000637)): ?>
<?php $component = $__componentOriginalf073577b00d5eaef97c81e52a3000637; ?>
<?php unset($__componentOriginalf073577b00d5eaef97c81e52a3000637); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginaleeccb55259fb1255815b60d773cef580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleeccb55259fb1255815b60d773cef580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.home-location-image','data' => ['breadcrumbs' => $breadcrumbs,'title' => 'Арена битв','imagePath' => 'assets/bannersBg/bannerHome.jpg','imageAlt' => 'Арена битв']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.home-location-image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs),'title' => 'Арена битв','imagePath' => 'assets/bannersBg/bannerHome.jpg','imageAlt' => 'Арена битв']); ?>
            
            <?php if (isset($component)) { $__componentOriginal330dedb9455611dafc44712602451f6f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal330dedb9455611dafc44712602451f6f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.welcome-message','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.welcome-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal330dedb9455611dafc44712602451f6f)): ?>
<?php $attributes = $__attributesOriginal330dedb9455611dafc44712602451f6f; ?>
<?php unset($__attributesOriginal330dedb9455611dafc44712602451f6f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal330dedb9455611dafc44712602451f6f)): ?>
<?php $component = $__componentOriginal330dedb9455611dafc44712602451f6f; ?>
<?php unset($__componentOriginal330dedb9455611dafc44712602451f6f); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleeccb55259fb1255815b60d773cef580)): ?>
<?php $attributes = $__attributesOriginaleeccb55259fb1255815b60d773cef580; ?>
<?php unset($__attributesOriginaleeccb55259fb1255815b60d773cef580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleeccb55259fb1255815b60d773cef580)): ?>
<?php $component = $__componentOriginaleeccb55259fb1255815b60d773cef580; ?>
<?php unset($__componentOriginaleeccb55259fb1255815b60d773cef580); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.game-flash-messages','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('game-flash-messages'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6)): ?>
<?php $attributes = $__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6; ?>
<?php unset($__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6)): ?>
<?php $component = $__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6; ?>
<?php unset($__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6); ?>
<?php endif; ?>

        
        <div class="bg-[#211f1a] px-2.5 py-3 shadow-inner">
            
            <div class="mb-4 text-sm text-[#d3c6a6] border-l-2 border-[#8c784e] pl-2">
                <p>Добро пожаловать на арену битв! Выберите локацию для сражений и испытайте свои силы в бою против
                    могущественных противников.</p>
            </div>

            
            <?php
                $isStunned = Auth::user()->isStunned();
            ?>
            <?php if (isset($component)) { $__componentOriginal2d5c916413972203857eb6e38bd5dcc1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2d5c916413972203857eb6e38bd5dcc1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.location-menu','data' => ['isStunned' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.location-menu'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2d5c916413972203857eb6e38bd5dcc1)): ?>
<?php $attributes = $__attributesOriginal2d5c916413972203857eb6e38bd5dcc1; ?>
<?php unset($__attributesOriginal2d5c916413972203857eb6e38bd5dcc1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2d5c916413972203857eb6e38bd5dcc1)): ?>
<?php $component = $__componentOriginal2d5c916413972203857eb6e38bd5dcc1; ?>
<?php unset($__componentOriginal2d5c916413972203857eb6e38bd5dcc1); ?>
<?php endif; ?>
        </div>
    </div>

    
    <?php
        $isStunned = Auth::user()->isStunned();
    ?>
    <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => ['isDisabled' => $isStunned]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['isDisabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.tooltip-modal','data' => ['id' => 'dungeon-tooltip','title' => 'Подземелье','icon' => 'assets/iconDungeon.png','description' => 'Исследуйте древние подземелья, сражайтесь с боссами и находите легендарные сокровища.','releaseDate' => 'Ожидаемая дата выхода: скоро']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.tooltip-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'dungeon-tooltip','title' => 'Подземелье','icon' => 'assets/iconDungeon.png','description' => 'Исследуйте древние подземелья, сражайтесь с боссами и находите легендарные сокровища.','releaseDate' => 'Ожидаемая дата выхода: скоро']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48)): ?>
<?php $attributes = $__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48; ?>
<?php unset($__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48)): ?>
<?php $component = $__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48; ?>
<?php unset($__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.tooltip-modal','data' => ['id' => 'trial-tooltip','title' => 'Испытание','icon' => 'assets/iconTrial.png','description' => 'Пройдите серию испытаний, проверьте свои навыки и получите уникальные награды за достижения.','releaseDate' => 'Ожидаемая дата выхода: следующее обновление']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.tooltip-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'trial-tooltip','title' => 'Испытание','icon' => 'assets/iconTrial.png','description' => 'Пройдите серию испытаний, проверьте свои навыки и получите уникальные награды за достижения.','releaseDate' => 'Ожидаемая дата выхода: следующее обновление']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48)): ?>
<?php $attributes = $__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48; ?>
<?php unset($__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48)): ?>
<?php $component = $__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48; ?>
<?php unset($__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.tooltip-modal','data' => ['id' => 'dominions-tooltip','title' => 'Доминионы','icon' => 'assets/iconDominions.png','description' => 'Масштабные сражения за территории между гильдиями. Захватывайте замки, собирайте дань и расширяйте свое влияние.','releaseDate' => 'Ожидаемая дата выхода: крупное обновление (Q3 2025)','requirements' => [
        ['icon' => '👤', 'text' => 'Уровень персонажа 30+'],
        ['icon' => '🏰', 'text' => 'Членство в гильдии']
    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.tooltip-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'dominions-tooltip','title' => 'Доминионы','icon' => 'assets/iconDominions.png','description' => 'Масштабные сражения за территории между гильдиями. Захватывайте замки, собирайте дань и расширяйте свое влияние.','releaseDate' => 'Ожидаемая дата выхода: крупное обновление (Q3 2025)','requirements' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['icon' => '👤', 'text' => 'Уровень персонажа 30+'],
        ['icon' => '🏰', 'text' => 'Членство в гильдии']
    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48)): ?>
<?php $attributes = $__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48; ?>
<?php unset($__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48)): ?>
<?php $component = $__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48; ?>
<?php unset($__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.tooltip-modal','data' => ['id' => 'events-tooltip','title' => 'Временные события','icon' => 'assets/iconTemporary.png','description' => 'Особые ограниченные по времени события с уникальными противниками, заданиями и наградами. Не пропустите!','releaseDate' => 'Первое событие: Праздник Зимнего Солнцестояния (декабрь 2025)','plannedEvents' => '8']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.tooltip-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'events-tooltip','title' => 'Временные события','icon' => 'assets/iconTemporary.png','description' => 'Особые ограниченные по времени события с уникальными противниками, заданиями и наградами. Не пропустите!','releaseDate' => 'Первое событие: Праздник Зимнего Солнцестояния (декабрь 2025)','plannedEvents' => '8']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48)): ?>
<?php $attributes = $__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48; ?>
<?php unset($__attributesOriginal80b3a55aa5c53ba65b2bc81724bf4d48); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48)): ?>
<?php $component = $__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48; ?>
<?php unset($__componentOriginal80b3a55aa5c53ba65b2bc81724bf4d48); ?>
<?php endif; ?>

</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/battle/index.blade.php ENDPATH**/ ?>