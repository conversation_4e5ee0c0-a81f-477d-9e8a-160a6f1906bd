<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'route' => '#',
    'icon' => '',
    'title' => '',
    'isActive' => true,
    'isBlocked' => false,
    'badge' => null,
    'tooltipId' => null,
    'onClick' => null,
    'isStunned' => false
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'route' => '#',
    'icon' => '',
    'title' => '',
    'isActive' => true,
    'isBlocked' => false,
    'badge' => null,
    'tooltipId' => null,
    'onClick' => null,
    'isStunned' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<?php if($isBlocked): ?>
    
    <div class="relative flex items-center text-left text-[#998d66] pl-0 pr-3 bg-gradient-to-r from-[#1d1916] to-[#2a2722] border-b border-[#3b3629] overflow-hidden transition-all duration-300">
        <span
            class="w-12 h-12 flex items-center justify-center mr-0 text-[#998d66] bg-gradient-to-br from-[#2a2722] to-[#1d1916] border-r border-[#3b3629] relative">
            <img src="<?php echo e(asset($icon)); ?>" alt="<?php echo e($title); ?>"
                class="w-8 h-8 filter grayscale opacity-40 drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]">
            
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-[#6e3f35] to-[#3c221b] rounded-full border border-[#998d66] flex items-center justify-center">
                <span class="text-[#f8eac2] text-xs">🔒</span>
            </div>
        </span>
        <span class="font-medium tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] ml-3 opacity-75"><?php echo e($title); ?></span>

        
        <span class="ml-auto flex items-center space-x-2">
            <div class="w-2 h-2 bg-gradient-to-r from-[#c1a96e] to-[#998d66] rounded-full animate-pulse shadow-[0_0_4px_rgba(193,169,110,0.4)]"></div>
            <span class="text-[#c1a96e] text-xs font-medium drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">На доработке</span>
        </span>

        
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent pointer-events-none"></div>

        
        <div class="absolute inset-0 opacity-5 pointer-events-none"
             style="background-image: repeating-linear-gradient(
                 45deg,
                 transparent,
                 transparent 10px,
                 rgba(193, 169, 110, 0.3) 10px,
                 rgba(193, 169, 110, 0.3) 20px
             );">
        </div>
    </div>
<?php elseif($isActive): ?>
    <a href="<?php echo e($isStunned ? '#' : $route); ?>"
        class="flex items-center text-left text-[#d4cbb0] pl-0 pr-3 bg-gradient-to-r from-[#1d1916] to-[#2a2722] border-b border-[#3b3629] transition-all duration-300 hover:from-[#2a2722] hover:to-[#3b3629] hover:text-[#e4d7b0] hover:shadow-[inset_0_0_10px_rgba(193,169,110,0.2)] <?php echo e($isStunned ? 'opacity-40 grayscale cursor-not-allowed pointer-events-none' : ''); ?>"
        <?php echo e($isStunned ? 'onclick="event.preventDefault(); return false;"' : ''); ?>>
        <span
            class="w-12 h-12 flex items-center justify-center mr-0 text-[#c1a96e] bg-gradient-to-br from-[#2a2722] to-[#1d1916] border-r border-[#3b3629] transition-all duration-300 hover:shadow-[0_0_8px_rgba(193,169,110,0.4)]">
            <img src="<?php echo e(asset($icon)); ?>" alt="<?php echo e($title); ?>"
                class="w-8 h-8 filter drop-shadow-[0_0_4px_rgba(193,169,110,0.5)] transition-all duration-300 hover:drop-shadow-[0_0_6px_rgba(193,169,110,0.7)]">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] ml-3 transition-all duration-300"><?php echo e($title); ?></span>
    </a>
<?php else: ?>
    <div class="flex items-center text-left text-[#998d66] pl-0 pr-3 bg-gradient-to-r from-[#1d1916] to-[#2a2722] border-b border-[#3b3629] relative group md:cursor-default cursor-pointer transition-all duration-300 hover:from-[#2a2722] hover:to-[#1d1916] hover:text-[#c1a96e]"
        <?php if($onClick): ?> onclick="<?php echo e($onClick); ?>" <?php endif; ?>>
        <span
            class="w-12 h-12 flex items-center justify-center mr-0 text-[#998d66] bg-gradient-to-br from-[#2a2722] to-[#1d1916] border-r border-[#3b3629] transition-all duration-300 group-hover:text-[#c1a96e]">
            <img src="<?php echo e(asset($icon)); ?>" alt="<?php echo e($title); ?>"
                class="w-8 h-8 filter grayscale opacity-60 drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] transition-all duration-300 group-hover:opacity-80">
        </span>
        <span
            class="font-medium tracking-wide drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)] ml-3 transition-all duration-300"><?php echo e($title); ?></span>

        <?php if($badge): ?>
            <span class="ml-auto">
                <span
                    class="inline-flex items-center px-2 py-1 rounded-sm bg-gradient-to-r from-[#2f473c] to-[#1e2e27] text-[#f8eac2] text-xs border border-[#3b3629] relative overflow-hidden shadow-[0_2px_4px_rgba(0,0,0,0.5)] transition-all duration-300 group-hover:shadow-[0_0_8px_rgba(193,169,110,0.3)]">
                    <span class="relative z-10 font-medium drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]"><?php echo e($badge); ?></span>
                    <span
                        class="absolute inset-0 bg-gradient-to-r from-[#2f473c] via-[#3b4a3f] to-[#2f473c] opacity-30 animate-pulse"></span>
                </span>
            </span>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/location-item.blade.php ENDPATH**/ ?>